<template>
  <el-dialog
    v-model="visible"
    title="历史咨询"
    width="80%"
    :before-close="handleClose"
    class="history-consult-dialog"
    top="5vh"
  >
    <div class="history-consult-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="专家姓名">
            <el-input
              v-model="searchForm.expertName"
              placeholder="请输入专家姓名"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="咨询标题">
            <el-input
              v-model="searchForm.title"
              placeholder="请输入咨询标题"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="回复状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="已回复" value="replied" />
              <el-option label="待回复" value="pending" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading" icon="Search">
              搜索
            </el-button>
            <el-button @click="handleReset" icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 咨询列表 -->
      <div class="consult-list" v-loading="loading">
        <div
          v-for="(item, index) in consultList"
          :key="item.id"
          class="consult-item"
          @click="handleItemClick(item)"
        >
          <div class="item-header">
            <div class="item-number">{{ index + 1 }}</div>
            <div class="expert-info">
              <img :src="item.expertAvatar || '/src/assets/imgs/avatar.jpg'" :alt="item.expertName" />
              <div class="expert-details">
                <div class="expert-name">{{ item.expertName }}</div>
                <div class="expert-title">{{ item.expertTitle }}</div>
              </div>
            </div>
            <div class="consult-title">{{ item.title }}</div>
            <div class="consult-time">{{ formatTime(item.submitTime) }}</div>
            <div class="consult-status">
              <el-tag
                :type="getStatusType(item.status)"
                :color="getStatusColor(item.status)"
                effect="plain"
              >
                {{ getStatusText(item.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="item-content">
            <div class="consult-info">
              <span class="label">咨询专家：</span>
              <span>{{ item.expertName }} {{ item.expertTitle }}</span>
            </div>
            <div class="consult-info">
              <span class="label">回复状态：</span>
              <span :style="{ color: getStatusColor(item.status) }">
                {{ getStatusText(item.status) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && consultList.length === 0" class="empty-state">
          <el-empty description="暂无历史咨询记录" />
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 咨询详情弹框 -->
    <ConsultDetailDialog
      :visible="detailVisible"
      :consultation="selectedConsultation"
      @close="handleDetailClose"
    />
  </el-dialog>
</template>

<script>
import { getConsultations } from './consultService.js'
import { statusMap, statusColorMap } from './consultDatabase.js'
import ConsultDetailDialog from './ConsultDetailDialog.vue'

export default {
  name: 'HistoryConsult',
  components: {
    ConsultDetailDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  data() {
    return {
      // 搜索表单
      searchForm: {
        expertName: '',
        title: '',
        status: ''
      },
      // 咨询列表
      consultList: [],
      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 加载状态
      loading: false,
      // 详情弹框
      detailVisible: false,
      selectedConsultation: null
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadConsultations()
      }
    }
  },
  methods: {
    // 加载咨询数据
    async loadConsultations() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          keyword: this.searchForm.title || this.searchForm.expertName,
          status: this.searchForm.status
        }

        const result = await getConsultations(params)
        if (result.success) {
          this.consultList = result.data.list
          this.pagination.total = result.data.total
        } else {
          this.$message.error(result.message || '获取数据失败')
        }
      } catch (error) {
        console.error('加载咨询数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadConsultations()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        expertName: '',
        title: '',
        status: ''
      }
      this.pagination.currentPage = 1
      this.loadConsultations()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadConsultations()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadConsultations()
    },

    // 点击咨询项
    handleItemClick(item) {
      this.selectedConsultation = item
      this.detailVisible = true
    },

    // 关闭详情弹框
    handleDetailClose() {
      this.detailVisible = false
      this.selectedConsultation = null
    },

    // 关闭弹框
    handleClose() {
      this.$emit('close')
    },

    // 格式化时间
    formatTime(timeStr) {
      const date = new Date(timeStr)
      return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },

    // 获取状态文本
    getStatusText(status) {
      return statusMap[status] || status
    },

    // 获取状态颜色
    getStatusColor(status) {
      return statusColorMap[status] || '#909399'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'pending': 'danger',
        'replied': 'success',
        'closed': 'info'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.history-consult-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.history-consult-container {
  height: 70vh;
  display: flex;
  flex-direction: column;

  .search-section {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;

        :deep(.el-form-item__label) {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .consult-list {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .consult-item {
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 16px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #4285f4;
        box-shadow: 0 2px 8px rgba(66, 133, 244, 0.15);
      }

      .item-header {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;

        .item-number {
          width: 40px;
          height: 40px;
          background: #4285f4;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-right: 16px;
        }

        .expert-info {
          display: flex;
          align-items: center;
          margin-right: 20px;

          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 12px;
          }

          .expert-details {
            .expert-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 4px;
            }

            .expert-title {
              font-size: 12px;
              color: #666;
            }
          }
        }

        .consult-title {
          flex: 1;
          font-weight: 500;
          color: #333;
          margin-right: 20px;
        }

        .consult-time {
          color: #999;
          font-size: 12px;
          margin-right: 16px;
        }

        .consult-status {
          min-width: 80px;
        }
      }

      .item-content {
        padding: 16px 20px;

        .consult-info {
          margin-bottom: 8px;
          font-size: 14px;

          .label {
            color: #666;
            margin-right: 8px;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }

  .pagination-container {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
  }
}
</style>
