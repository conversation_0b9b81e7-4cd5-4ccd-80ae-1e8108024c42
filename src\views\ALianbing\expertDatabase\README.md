# 专家咨询系统

## 功能概述

本系统实现了完整的专家咨询功能，包括用户提交咨询、专家回复、咨询管理等功能。

## 文件结构

```
src/views/ALianbing/expertDatabase/
├── index.vue                    # 专家数据库主页面
├── consult.vue                  # 咨询弹框组件
├── consultDatabase.js           # 模拟数据库（咨询数据存储）
├── consultManagement.vue        # 咨询管理页面
├── consultDetailDialog.vue      # 咨询详情弹框
├── consultReplyDialog.vue       # 回复咨询弹框
├── testConsult.vue             # 测试页面
└── README.md                   # 说明文档
```

## 主要功能

### 1. 用户咨询功能 (consult.vue)

- **功能描述**: 用户点击"立即咨询"按钮后弹出的咨询表单
- **主要特性**:
  - 富文本编辑器支持格式化文本
  - 表单验证（标题、联系方式、内容）
  - 字数限制和实时统计
  - 支持插入链接、图片等
  - 响应式设计

- **使用方式**:
  ```vue
  <ExpertConsult 
    :visible="consultVisible" 
    :expert="selectedExpert" 
    @close="handleConsultClose" 
  />
  ```

### 2. 咨询数据存储 (consultDatabase.js)

- **功能描述**: 模拟数据库，用于存储和管理咨询信息
- **主要功能**:
  - `saveConsultation()` - 保存新的咨询
  - `getConsultations()` - 获取咨询列表（支持分页、筛选）
  - `getConsultationById()` - 获取单个咨询详情
  - `replyConsultation()` - 专家回复咨询
  - `deleteConsultation()` - 删除咨询
  - `getConsultationStats()` - 获取统计信息

- **数据存储**: 使用 localStorage 进行本地存储

### 3. 咨询管理页面 (consultManagement.vue)

- **功能描述**: 管理员/专家查看和管理所有咨询的页面
- **主要特性**:
  - 咨询列表展示
  - 搜索和筛选功能
  - 统计信息展示
  - 查看详情、回复、删除操作
  - 分页功能

### 4. 咨询详情弹框 (consultDetailDialog.vue)

- **功能描述**: 显示咨询的详细信息
- **主要特性**:
  - 完整的咨询信息展示
  - 富文本内容渲染
  - 专家回复显示
  - 支持直接回复操作

### 5. 回复咨询弹框 (consultReplyDialog.vue)

- **功能描述**: 专家回复咨询的表单
- **主要特性**:
  - 显示原咨询内容
  - 回复内容编辑
  - 表单验证
  - 字数限制

## 数据结构

### 咨询数据结构
```javascript
{
  id: 1,                          // 咨询ID
  expertId: 1,                    // 专家ID
  expertName: '王强',              // 专家姓名
  title: '咨询标题',               // 咨询标题
  contact: '联系方式',             // 联系方式
  content: '<p>富文本内容</p>',     // 富文本内容
  contentText: '纯文本内容',        // 纯文本内容
  submitTime: '2024-01-15T10:30:00.000Z', // 提交时间
  status: 'pending',              // 状态: pending/replied/closed
  reply: {                        // 回复信息（可选）
    content: '回复内容',
    replyTime: '2024-01-15T14:20:00.000Z'
  }
}
```

## 使用说明

### 1. 在专家数据库页面集成咨询功能

在 `index.vue` 中已经集成了咨询功能：

```vue
<!-- 咨询弹框 -->
<ExpertConsult 
  :visible="consultVisible" 
  :expert="selectedExpert" 
  @close="handleConsultClose" 
/>
```

点击专家卡片上的"立即咨询"按钮即可打开咨询弹框。

### 2. 查看和管理咨询

可以通过 `consultManagement.vue` 页面查看和管理所有咨询：

- 搜索和筛选咨询
- 查看咨询详情
- 回复咨询
- 删除咨询
- 查看统计信息

### 3. 测试功能

使用 `testConsult.vue` 页面可以测试咨询功能：

- 测试咨询弹框
- 查看本地存储的数据
- 验证功能是否正常

## 样式特性

- 使用渐变色主题设计
- 响应式布局，支持移动端
- 统一的卡片式设计风格
- 丰富的交互效果和动画
- Element Plus 组件库集成

## 扩展建议

1. **后端集成**: 将 `consultDatabase.js` 替换为真实的 API 调用
2. **实时通知**: 添加新咨询的实时通知功能
3. **邮件通知**: 咨询提交和回复时发送邮件通知
4. **文件上传**: 支持在咨询中上传附件
5. **评价系统**: 添加用户对专家回复的评价功能
6. **权限管理**: 添加不同角色的权限控制

## 注意事项

1. 当前使用 localStorage 存储数据，刷新页面数据不会丢失
2. 富文本编辑器使用了 `document.execCommand`，在某些现代浏览器中可能有兼容性警告
3. 建议在生产环境中替换为更现代的富文本编辑器解决方案
4. 数据验证主要在前端进行，后端集成时需要添加服务端验证
