// 咨询服务 - 包含所有业务逻辑
import { mockConsultations } from './consultDatabase.js'

// 本地存储的数据
let consultations = [...mockConsultations]

// 生成唯一ID
let nextId = Math.max(...consultations.map(c => c.id)) + 1

/**
 * 保存咨询信息
 * @param {Object} consultData 咨询数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveConsultation = async (consultData) => {
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 创建新的咨询记录
    const newConsultation = {
      id: nextId++,
      ...consultData,
      submitTime: new Date().toISOString(),
      status: 'pending'
    }
    
    // 添加到数据存储
    consultations.unshift(newConsultation)
    
    // 模拟保存到本地存储
    localStorage.setItem('expertConsultations', JSON.stringify(consultations))
    
    console.log('新增咨询记录:', newConsultation)
    
    return {
      success: true,
      data: newConsultation,
      message: '咨询提交成功'
    }
  } catch (error) {
    console.error('保存咨询信息失败:', error)
    return {
      success: false,
      message: '保存失败，请重试'
    }
  }
}

/**
 * 获取所有咨询信息
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 咨询列表
 */
export const getConsultations = async (params = {}) => {
  try {
    // 从本地存储加载数据
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }
    
    let result = [...consultations]
    
    // 按专家ID筛选
    if (params.expertId) {
      result = result.filter(item => item.expertId === params.expertId)
    }
    
    // 按状态筛选
    if (params.status) {
      result = result.filter(item => item.status === params.status)
    }
    
    // 按关键词搜索
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      result = result.filter(item => 
        item.title.toLowerCase().includes(keyword) ||
        item.contentText.toLowerCase().includes(keyword)
      )
    }
    
    // 分页处理
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const total = result.length
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const list = result.slice(start, end)
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return {
      success: true,
      data: {
        list,
        total,
        page,
        pageSize
      }
    }
  } catch (error) {
    console.error('获取咨询列表失败:', error)
    return {
      success: false,
      message: '获取数据失败'
    }
  }
}

/**
 * 根据ID获取咨询详情
 * @param {number} id 咨询ID
 * @returns {Promise<Object>} 咨询详情
 */
export const getConsultationById = async (id) => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }
    
    const consultation = consultations.find(item => item.id === id)
    
    if (consultation) {
      return {
        success: true,
        data: consultation
      }
    } else {
      return {
        success: false,
        message: '咨询记录不存在'
      }
    }
  } catch (error) {
    console.error('获取咨询详情失败:', error)
    return {
      success: false,
      message: '获取详情失败'
    }
  }
}

/**
 * 专家回复咨询
 * @param {number} id 咨询ID
 * @param {string} replyContent 回复内容
 * @returns {Promise<Object>} 回复结果
 */
export const replyConsultation = async (id, replyContent) => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }
    
    const index = consultations.findIndex(item => item.id === id)
    
    if (index !== -1) {
      consultations[index].status = 'replied'
      consultations[index].reply = {
        content: replyContent,
        replyTime: new Date().toISOString()
      }
      
      // 保存到本地存储
      localStorage.setItem('expertConsultations', JSON.stringify(consultations))
      
      return {
        success: true,
        data: consultations[index],
        message: '回复成功'
      }
    } else {
      return {
        success: false,
        message: '咨询记录不存在'
      }
    }
  } catch (error) {
    console.error('回复咨询失败:', error)
    return {
      success: false,
      message: '回复失败，请重试'
    }
  }
}

/**
 * 删除咨询记录
 * @param {number} id 咨询ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteConsultation = async (id) => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }
    
    const index = consultations.findIndex(item => item.id === id)
    
    if (index !== -1) {
      consultations.splice(index, 1)
      
      // 保存到本地存储
      localStorage.setItem('expertConsultations', JSON.stringify(consultations))
      
      return {
        success: true,
        message: '删除成功'
      }
    } else {
      return {
        success: false,
        message: '咨询记录不存在'
      }
    }
  } catch (error) {
    console.error('删除咨询失败:', error)
    return {
      success: false,
      message: '删除失败，请重试'
    }
  }
}

/**
 * 获取咨询统计信息
 * @returns {Promise<Object>} 统计信息
 */
export const getConsultationStats = async () => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }
    
    const total = consultations.length
    const pending = consultations.filter(item => item.status === 'pending').length
    const replied = consultations.filter(item => item.status === 'replied').length
    const closed = consultations.filter(item => item.status === 'closed').length
    
    return {
      success: true,
      data: {
        total,
        pending,
        replied,
        closed
      }
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    return {
      success: false,
      message: '获取统计信息失败'
    }
  }
}
