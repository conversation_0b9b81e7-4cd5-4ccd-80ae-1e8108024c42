<template>
  <el-dialog
    v-model="visible"
    title="咨询详情"
    width="60%"
    :before-close="handleClose"
    class="consult-detail-dialog"
    top="5vh"
  >
    <div class="consult-detail-container" v-if="consultation">
      <!-- 咨询信息 -->
      <div class="consult-section">
        <div class="section-header">
          <h3>咨询信息</h3>
          <el-tag
            :type="getStatusType(consultation.status)"
            :color="getStatusColor(consultation.status)"
            effect="plain"
          >
            {{ getStatusText(consultation.status) }}
          </el-tag>
        </div>
        
        <div class="consult-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">咨询标题：</span>
              <span class="value">{{ consultation.title }}</span>
            </div>
            <div class="info-item">
              <span class="label">提交时间：</span>
              <span class="value">{{ formatTime(consultation.submitTime) }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="label">咨询专家：</span>
              <span class="value">{{ consultation.expertName }} {{ consultation.expertTitle }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系方式：</span>
              <span class="value">{{ consultation.contact }}</span>
            </div>
          </div>
          
          <div class="info-row full-width">
            <div class="info-item">
              <span class="label">咨询内容：</span>
            </div>
          </div>
          
          <div class="content-box">
            <div v-html="consultation.content" class="content-html"></div>
          </div>
        </div>
      </div>

      <!-- 专家回复 -->
      <div class="reply-section" v-if="consultation.reply">
        <div class="section-header">
          <h3>专家回复</h3>
          <span class="reply-time">{{ formatTime(consultation.reply.replyTime) }}</span>
        </div>
        
        <div class="reply-content">
          <div class="reply-box">
            <div class="reply-text">{{ consultation.reply.content }}</div>
          </div>
        </div>
      </div>

      <!-- 未回复状态 -->
      <div class="no-reply-section" v-else>
        <div class="section-header">
          <h3>专家回复</h3>
        </div>
        
        <div class="no-reply-content">
          <el-empty description="专家暂未回复，请耐心等待" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { statusMap, statusColorMap } from './consultDatabase.js'

export default {
  name: 'ConsultDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    consultation: {
      type: Object,
      default: null
    }
  },
  emits: ['close'],
  methods: {
    // 关闭弹框
    handleClose() {
      this.$emit('close')
    },

    // 格式化时间
    formatTime(timeStr) {
      const date = new Date(timeStr)
      return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },

    // 获取状态文本
    getStatusText(status) {
      return statusMap[status] || status
    },

    // 获取状态颜色
    getStatusColor(status) {
      return statusColorMap[status] || '#909399'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'pending': 'danger',
        'replied': 'success',
        'closed': 'info'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.consult-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.consult-detail-container {
  max-height: 70vh;
  overflow-y: auto;

  .consult-section,
  .reply-section,
  .no-reply-section {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        color: #333;
        font-size: 18px;
        font-weight: 600;
      }

      .reply-time {
        color: #666;
        font-size: 14px;
      }
    }

    .consult-info {
      .info-row {
        display: flex;
        margin-bottom: 16px;

        &.full-width {
          flex-direction: column;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: flex-start;

          .label {
            color: #666;
            font-weight: 500;
            min-width: 100px;
            margin-right: 12px;
          }

          .value {
            color: #333;
            flex: 1;
          }
        }
      }

      .content-box {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 16px;
        margin-top: 8px;

        .content-html {
          color: #333;
          line-height: 1.6;

          :deep(p) {
            margin: 0 0 12px 0;

            &:last-child {
              margin-bottom: 0;
            }
          }

          :deep(strong) {
            font-weight: 600;
            color: #4285f4;
          }

          :deep(em) {
            font-style: italic;
            color: #e74c3c;
          }

          :deep(ul), :deep(ol) {
            margin: 8px 0;
            padding-left: 24px;
          }

          :deep(li) {
            margin-bottom: 4px;
          }
        }
      }
    }

    .reply-content {
      .reply-box {
        background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
        border: 1px solid #d1e9ff;
        border-radius: 8px;
        padding: 20px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -8px;
          left: 24px;
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 8px solid #d1e9ff;
        }

        &::after {
          content: '';
          position: absolute;
          top: -7px;
          left: 24px;
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 8px solid #e8f4fd;
        }

        .reply-text {
          color: #333;
          line-height: 1.6;
          font-size: 15px;
        }
      }
    }

    .no-reply-content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 120px;
    }
  }
}

.dialog-footer {
  padding: 16px 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}
</style>
